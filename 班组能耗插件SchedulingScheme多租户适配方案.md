# 班组能耗插件SchedulingScheme多租户适配方案

## 1. 工程分析

### 1.1 涉及的实体模型
- **SchedulingScheme**: 排班方案主实体
- **ClassesScheme**: 班次方案实体（子实体）
- **TeamGroupInfo**: 班组信息实体（子实体）
- **SchedulingSchemeToNode**: 排班方案关联节点实体

### 1.2 业务逻辑分析
排班方案是班组能耗管理的核心配置，包含：
- 排班方案的基本信息（名称、类型、创建时间等）
- 关联的班次方案配置
- 关联的班组信息
- 与节点的关联关系

需要实现租户级别和项目级别的数据隔离。

## 2. 实体类修改

### 2.1 SchedulingScheme实体类修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java`

**修改内容**:
```java
// 在类中添加以下三个字段
@ApiModelProperty("租户ID")
@JsonProperty("tenantid")
private Long tenantId;

@ApiModelProperty("根节点ID")
@JsonProperty("rootnodeid")
private Long rootNodeId;

@ApiModelProperty("根节点Label")
@JsonProperty("rootnodelabel")
private String rootNodeLabel;
```

**修改构造函数**:
```java
public SchedulingScheme(SchedulingSchemeAddUpdateDTO dto) {
    this.id = dto.getId();
    this.name = dto.getName();
    this.classTeamType = dto.getClassTeamType();
    this.createTime = System.currentTimeMillis();
    this.operator = dto.getOperator();
    // 新增多租户字段
    this.tenantId = dto.getTenantId();
    this.rootNodeId = dto.getRootNodeId();
    this.rootNodeLabel = dto.getRootNodeLabel();
    this.modelLabel = TableNameDef.SCHEDULING_SCHEME;
}
```

## 3. DTO类修改

### 3.1 SchedulingSchemeAddUpdateDTO修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/dto/SchedulingSchemeAddUpdateDTO.java`

**修改内容**:
```java
// 添加以下字段
@ApiModelProperty("根节点id")
private Long rootNodeId;

@ApiModelProperty("根节点label")
private String rootNodeLabel;

// 注意：tenantId将通过Controller层的@RequestHeader获取，不需要在DTO中定义
```

### 3.2 SchedulingSchemeQueryDTO修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/dto/SchedulingSchemeQueryDTO.java`

**修改内容**:
```java
// 添加以下字段
@ApiModelProperty("根节点")
@NotNull(message = "根节点不能为空")
private BaseEntity rootNode;
```

## 4. Controller层修改

### 4.1 TeamConfigController修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java`

**需要修改的接口**:

#### 4.1.1 新增修改排班方案接口
```java
@PostMapping("/schedulingscheme/addOrUpdate")
@ApiOperation("新增修改排班方案")
@OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【新增编辑排班方案】")
@OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_INSPECT, GroupEnergyConstantDef.SCHEDULINGSCHEME_UPDATE}, andOr = EnumAndOr.OR)
public Result<Boolean> addOrUpdateSchedulingScheme(
        @NotNull @RequestHeader(ColumnDef.TENANT_HEADER) Long tenantId,
        @RequestBody SchedulingSchemeAddUpdateDTO dto) {
    return Result.ok(teamConfigService.addOrUpdateSchedulingScheme(tenantId, dto));
}
```

#### 4.1.2 查询排班方案接口
```java
@PostMapping("/schedulingscheme/query")
@ApiOperation("根据条件查询排班方案")
public ResultWithTotal<List<SchedulingSchemeDetailVO>> querySchedulingScheme(
        @NotNull @RequestHeader(ColumnDef.TENANT_HEADER) Long tenantId,
        @RequestBody SchedulingSchemeQueryDTO dto) {
    return teamConfigService.querySchedulingScheme(tenantId, dto);
}
```

#### 4.1.3 其他查询接口修改
```java
@GetMapping("/schedulingscheme/type/query")
@ApiOperation("根据方案类型查询排班方案")
public Result<List<SchedulingSchemeDetailVO>> querySchedulingSchemeByType(
        @NotNull @RequestHeader(ColumnDef.TENANT_HEADER) Long tenantId,
        @RequestParam(required = false) @ApiParam(name = "classTeamType", value = "排班方案类型", required = false) Integer classTeamType,
        @RequestParam @ApiParam(name = "rootNodeId", value = "根节点ID", required = true) Long rootNodeId,
        @RequestParam @ApiParam(name = "rootNodeLabel", value = "根节点Label", required = true) String rootNodeLabel) {
    return Result.ok(teamConfigService.queryProduceSchedulingSchemeByType(tenantId, classTeamType, rootNodeId, rootNodeLabel));
}

@GetMapping("/schedulingscheme/all/query")
@ApiOperation("查询所有排班方案")
@OperationPermission(authNames = {GroupEnergyConstantDef.SCHEDULINGSCHEME_BROWSER})
public Result<List<SchedulingSchemeDetailVO>> allSchedulingScheme(
        @NotNull @RequestHeader(ColumnDef.TENANT_HEADER) Long tenantId,
        @RequestParam @ApiParam(name = "rootNodeId", value = "根节点ID", required = true) Long rootNodeId,
        @RequestParam @ApiParam(name = "rootNodeLabel", value = "根节点Label", required = true) String rootNodeLabel) {
    return Result.ok(teamConfigService.allSchedulingScheme(tenantId, rootNodeId, rootNodeLabel));
}
```

## 5. Service层修改

### 5.1 TeamConfigService接口修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java`

**修改方法签名**:
```java
// 新增修改排班方案
Boolean addOrUpdateSchedulingScheme(Long tenantId, SchedulingSchemeAddUpdateDTO dto);

// 查询排班方案
ResultWithTotal<List<SchedulingSchemeDetailVO>> querySchedulingScheme(Long tenantId, SchedulingSchemeQueryDTO dto);

// 查询所有排班方案
List<SchedulingSchemeDetailVO> allSchedulingScheme(Long tenantId, Long rootNodeId, String rootNodeLabel);

// 根据类型查询排班方案
List<SchedulingSchemeDetailVO> queryProduceSchedulingSchemeByType(Long tenantId, Integer classTeamType, Long rootNodeId, String rootNodeLabel);
```

### 5.2 TeamConfigServiceImpl实现类修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java`

**主要修改点**:
1. 在新增修改方法中设置多租户字段
2. 在查询方法中添加多租户过滤条件
3. 传递tenantId和rootNode信息到Dao层

## 6. Dao层修改

### 6.1 SchedulingSchemeDao接口修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java`

**修改方法签名**:
```java
// 查询排班方案
ResultWithTotal<List<SchedulingScheme>> pageQuery(Long tenantId, SchedulingSchemeQueryDTO dto);

// 查询所有排班方案
List<SchedulingScheme> queryAll(Long tenantId, Long rootNodeId, String rootNodeLabel);

// 查询生产类型排班方案
List<SchedulingScheme> queryProduceSchedulingScheme(Long tenantId, Integer classTeamType, Long rootNodeId, String rootNodeLabel);
```

### 6.2 SchedulingSchemeDaoImpl实现类修改

**文件路径**: `eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java`

**主要修改点**:
1. 在查询条件中添加tenantId过滤
2. 在查询条件中添加rootNodeId和rootNodeLabel过滤
3. 更新所有查询方法的实现

## 7. 修改优先级和风险评估

### 7.1 修改优先级
1. **高优先级**: 实体类、DTO类修改（影响数据结构）
2. **中优先级**: Controller层修改（影响接口契约）
3. **低优先级**: Service和Dao层修改（内部实现）

### 7.2 风险评估
- **数据兼容性风险**: 需要考虑现有数据的迁移
- **接口兼容性风险**: 前端调用需要同步修改
- **测试风险**: 需要全面测试多租户隔离效果

## 8. 实施建议

1. **分阶段实施**: 先修改实体类和DTO，再修改Controller，最后修改Service和Dao
2. **数据迁移**: 需要为现有数据设置默认的tenantId和rootNode值
3. **测试验证**: 重点测试数据隔离效果和接口功能完整性
4. **文档更新**: 更新API文档和使用说明

## 9. 注意事项

1. 确保ColumnDef.TENANT_HEADER常量已定义
2. 需要导入相关的注解和工具类
3. 考虑与其他模块的依赖关系
4. 保持代码风格的一致性
